#!/usr/bin/env python3
"""
Excel File Reader - Read and display Excel files as tables
"""

import pandas as pd
import os
from pathlib import Path
import sys

def list_excel_files(directory="database"):
    """List all Excel files in the specified directory"""
    excel_files = []
    if os.path.exists(directory):
        for file in os.listdir(directory):
            if file.endswith(('.xlsx', '.xls')):
                excel_files.append(os.path.join(directory, file))
    return excel_files

def read_excel_file(file_path, sheet_name=None):
    """Read an Excel file and return as pandas DataFrame"""
    try:
        # Read the Excel file
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        else:
            df = pd.read_excel(file_path)
        
        return df
    except Exception as e:
        print(f"Error reading {file_path}: {str(e)}")
        return None

def get_sheet_names(file_path):
    """Get all sheet names from an Excel file"""
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except Exception as e:
        print(f"Error getting sheet names from {file_path}: {str(e)}")
        return []

def display_table(df, file_name, sheet_name=None):
    """Display DataFrame as a formatted table"""
    if df is None:
        return
    
    print(f"\n{'='*80}")
    if sheet_name:
        print(f"File: {file_name} | Sheet: {sheet_name}")
    else:
        print(f"File: {file_name}")
    print(f"Shape: {df.shape[0]} rows × {df.shape[1]} columns")
    print(f"{'='*80}")
    
    # Display basic info about the DataFrame
    print("\nColumn Information:")
    print("-" * 40)
    for i, col in enumerate(df.columns):
        dtype = df[col].dtype
        non_null = df[col].count()
        print(f"{i+1:2d}. {col:<25} | {dtype:<10} | {non_null} non-null")
    
    print(f"\n{'='*80}")
    print("Data Preview (first 10 rows):")
    print(f"{'='*80}")
    
    # Configure pandas display options for better table formatting
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 20)
    
    # Display the first 10 rows
    print(df.head(10).to_string(index=True))
    
    if len(df) > 10:
        print(f"\n... and {len(df) - 10} more rows")

def main():
    """Main function to read and display Excel files"""
    print("Excel File Reader")
    print("=" * 50)
    
    # List available Excel files
    excel_files = list_excel_files()
    
    if not excel_files:
        print("No Excel files found in the 'database' directory.")
        return
    
    print(f"\nFound {len(excel_files)} Excel file(s):")
    for i, file in enumerate(excel_files, 1):
        print(f"{i}. {os.path.basename(file)}")
    
    # If command line argument provided, use it as file selection
    if len(sys.argv) > 1:
        try:
            file_index = int(sys.argv[1]) - 1
            if 0 <= file_index < len(excel_files):
                selected_file = excel_files[file_index]
            else:
                print(f"Invalid file number. Please choose between 1 and {len(excel_files)}")
                return
        except ValueError:
            # Treat as filename
            filename = sys.argv[1]
            selected_file = None
            for file in excel_files:
                if filename in os.path.basename(file):
                    selected_file = file
                    break
            if not selected_file:
                print(f"File '{filename}' not found.")
                return
    else:
        # Interactive selection
        try:
            choice = input(f"\nSelect a file (1-{len(excel_files)}): ")
            file_index = int(choice) - 1
            if 0 <= file_index < len(excel_files):
                selected_file = excel_files[file_index]
            else:
                print(f"Invalid choice. Please choose between 1 and {len(excel_files)}")
                return
        except (ValueError, KeyboardInterrupt):
            print("\nOperation cancelled.")
            return
    
    print(f"\nSelected file: {os.path.basename(selected_file)}")
    
    # Get sheet names
    sheet_names = get_sheet_names(selected_file)
    if not sheet_names:
        return
    
    print(f"Available sheets: {', '.join(sheet_names)}")
    
    # If multiple sheets, ask which one to display
    if len(sheet_names) > 1:
        print(f"\nFound {len(sheet_names)} sheets:")
        for i, sheet in enumerate(sheet_names, 1):
            print(f"{i}. {sheet}")
        
        try:
            sheet_choice = input(f"\nSelect a sheet (1-{len(sheet_names)}) or press Enter for all: ")
            if sheet_choice.strip():
                sheet_index = int(sheet_choice) - 1
                if 0 <= sheet_index < len(sheet_names):
                    selected_sheet = sheet_names[sheet_index]
                    df = read_excel_file(selected_file, selected_sheet)
                    display_table(df, os.path.basename(selected_file), selected_sheet)
                else:
                    print(f"Invalid choice. Please choose between 1 and {len(sheet_names)}")
                    return
            else:
                # Display all sheets
                for sheet in sheet_names:
                    df = read_excel_file(selected_file, sheet)
                    display_table(df, os.path.basename(selected_file), sheet)
        except (ValueError, KeyboardInterrupt):
            print("\nOperation cancelled.")
            return
    else:
        # Single sheet
        df = read_excel_file(selected_file)
        display_table(df, os.path.basename(selected_file))

if __name__ == "__main__":
    main()
