import pandas as pd
import numpy as np

from pathlib import Path
import sys

from excel_reader import *

file_path = list_excel_files()
file_path

for file in file_path:
    df = read_excel_file(file)

df

df.columns = df.iloc[3]
df = df[4:-1].reset_index(drop=True)

df

 # Key methods in ExcelTemplateHandler:
- load_template()           # Load Excel file preserving formatting
- update_cell()            # Update individual cells
- update_row()             # Update entire rows
- insert_row()             # Insert new rows with formatting
- find_rows_by_criteria()  # Find rows matching conditions
- read_data_as_dataframe() # Convert to pandas DataFrame
- save_template()          # Save with formatting intact